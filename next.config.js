/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '3000',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'localhost',
        pathname: '/**',
      },
    ],
  },
  env: {
    NEXTAUTH_URL: process.env.NEXTAUTH_URL,
    NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET,
  },
  experimental: {
    optimizePackageImports: ['lucide-react'],
  },
  // Enable React strict mode
  reactStrictMode: true,
  // Disable static generation for authenticated pages
  trailingSlash: false,
  // Enhanced webpack config to fix Next.js 15 router issues
  webpack: (config, { isServer, dev }) => {
    // Fix for Next.js 15 router utilities
    config.resolve.alias = {
      ...config.resolve.alias,
      // Ensure proper resolution of Next.js internals
      'next/dist/shared/lib/router/utils/is-dynamic': require.resolve('next/dist/shared/lib/router/utils/is-dynamic'),
      'next/dist/shared/lib/router/utils/index': require.resolve('next/dist/shared/lib/router/utils/index'),
      'next/dist/client/resolve-href': require.resolve('next/dist/client/resolve-href'),
      'next/dist/client/link': require.resolve('next/dist/client/link'),
    };

    // Only add fallbacks for server-side rendering
    if (isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
        crypto: false,
        stream: false,
        url: false,
        zlib: false,
        http: false,
        https: false,
        assert: false,
        os: false,
        path: false,
      };
    }

    // Fix for dynamic imports in Next.js 15
    config.module.rules.push({
      test: /\.m?js$/,
      type: 'javascript/auto',
      resolve: {
        fullySpecified: false,
      },
    });

    return config;
  },
  // Fix for Next.js 15 hydration issues
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },
};

module.exports = nextConfig;
